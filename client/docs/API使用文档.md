# ChatGPT Controller API 使用文档

## 概述

ChatGPT Controller API 是一个用于管理 ChatGPT 对话的 RESTful API 服务。本文档介绍如何使用该 API 进行系统状态查询、对话管理和消息发送等操作。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: `v1`
- **完整API地址**: `http://localhost:8000/api/v1`
- **数据格式**: JSON
- **字符编码**: UTF-8

## API 端点概览

### 1. 系统状态相关

| 端点 | 方法 | 描述 |
|------|------|------|
| `/status` | GET | 获取系统完整状态信息 |
| `/health` | GET | 快速健康检查 |
| `/metrics` | GET | 获取详细系统指标 |

### 2. 对话管理相关

| 端点 | 方法 | 描述 |
|------|------|------|
| `/conversations` | GET | 获取对话列表 |
| `/conversations` | POST | 创建新对话 |
| `/conversations/{conversation_id}` | GET | 获取特定对话详情 |
| `/conversations/{conversation_id}/messages` | POST | 向对话发送消息 |

## 详细API说明

### 1. 获取系统状态

**端点**: `GET /api/v1/status`

**描述**: 获取系统完整状态信息，包括连接状态、系统健康度和性能指标。

**请求示例**:
```http
GET /api/v1/status HTTP/1.1
Host: localhost:8000
Content-Type: application/json
```

**响应示例**:
```json
{
  "success": true,
  "message": "系统运行正常",
  "timestamp": "2024-01-15T10:30:00Z",
  "connection_status": {
    "connected": true,
    "client_count": 3,
    "uptime_seconds": 86400.5
  },
  "system_health": {
    "database_healthy": true,
    "websocket_healthy": true,
    "memory_usage_mb": 256.7,
    "cpu_usage_percent": 15.2
  }
}
```

### 2. 快速健康检查

**端点**: `GET /api/v1/health`

**描述**: 快速检查系统是否正常运行。

**请求示例**:
```http
GET /api/v1/health HTTP/1.1
Host: localhost:8000
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 3. 获取对话列表

**端点**: `GET /api/v1/conversations`

**描述**: 获取对话列表，支持分页和筛选。

**查询参数**:
- `page` (可选): 页码，默认为1
- `page_size` (可选): 每页数量，默认为20，最大100
- `status` (可选): 对话状态筛选 (`active`, `archived`, `deleted`)
- `mode` (可选): 对话模式筛选 (`research`, `reason`, `search`, `canvas`, `picture_v2`)

**请求示例**:
```http
GET /api/v1/conversations?page=1&page_size=10&status=active HTTP/1.1
Host: localhost:8000
Content-Type: application/json
```

**响应示例**:
```json
{
  "success": true,
  "conversations": [
    {
      "id": "conv_123456789",
      "title": "关于人工智能的讨论",
      "mode": "research",
      "status": "active",
      "message_count": 15,
      "created_at": "2024-01-15T09:00:00Z",
      "updated_at": "2024-01-15T10:25:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_pages": 5,
    "total_count": 47
  }
}
```

### 4. 创建新对话

**端点**: `POST /api/v1/conversations`

**描述**: 创建一个新的ChatGPT对话。

**请求体参数**:
- `initial_prompt` (必需): 初始提示词
- `mode` (可选): 对话模式，默认为 `research`
- `title` (可选): 对话标题

**请求示例**:
```http
POST /api/v1/conversations HTTP/1.1
Host: localhost:8000
Content-Type: application/json

{
  "initial_prompt": "你好，我想了解一下人工智能的发展历史",
  "mode": "research",
  "title": "AI发展历史讨论"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "对话创建成功",
  "conversation": {
    "id": "conv_987654321",
    "title": "AI发展历史讨论",
    "mode": "research",
    "status": "active",
    "message_count": 1,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 5. 获取对话详情

**端点**: `GET /api/v1/conversations/{conversation_id}`

**描述**: 获取特定对话的详细信息和消息历史。

**路径参数**:
- `conversation_id`: 对话ID

**请求示例**:
```http
GET /api/v1/conversations/conv_123456789 HTTP/1.1
Host: localhost:8000
Content-Type: application/json
```

**响应示例**:
```json
{
  "success": true,
  "conversation": {
    "id": "conv_123456789",
    "title": "关于人工智能的讨论",
    "mode": "research",
    "status": "active",
    "message_count": 3,
    "created_at": "2024-01-15T09:00:00Z",
    "updated_at": "2024-01-15T10:25:00Z",
    "messages": [
      {
        "id": "msg_001",
        "role": "user",
        "content": "你好，我想了解一下人工智能的发展历史",
        "timestamp": "2024-01-15T09:00:00Z"
      },
      {
        "id": "msg_002",
        "role": "assistant",
        "content": "您好！我很乐意为您介绍人工智能的发展历史...",
        "timestamp": "2024-01-15T09:00:15Z"
      }
    ]
  }
}
```

### 6. 发送消息

**端点**: `POST /api/v1/conversations/{conversation_id}/messages`

**描述**: 向指定对话发送新消息。

**路径参数**:
- `conversation_id`: 对话ID

**请求体参数**:
- `content` (必需): 消息内容

**请求示例**:
```http
POST /api/v1/conversations/conv_123456789/messages HTTP/1.1
Host: localhost:8000
Content-Type: application/json

{
  "content": "请详细介绍一下深度学习的发展"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "消息发送成功",
  "message_id": "msg_003",
  "conversation_updated": true
}
```

## 对话模式说明

| 模式 | 英文名称 | 描述 |
|------|----------|------|
| 研究模式 | `research` | 适合深入研究和学术讨论 |
| 推理模式 | `reason` | 适合逻辑推理和问题解决 |
| 搜索模式 | `search` | 适合信息搜索和查询 |
| 画布模式 | `canvas` | 适合创意和设计工作 |
| 图片模式 | `picture_v2` | 适合图像相关任务 |

## 对话状态说明

| 状态 | 英文名称 | 描述 |
|------|----------|------|
| 活跃 | `active` | 正在进行的对话 |
| 已归档 | `archived` | 已完成并归档的对话 |
| 已删除 | `deleted` | 已删除的对话 |

## 错误处理

API 使用标准的 HTTP 状态码来表示请求结果：

- `200 OK`: 请求成功
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 资源不存在
- `500 Internal Server Error`: 服务器内部错误

**错误响应格式**:
```json
{
  "success": false,
  "error": "错误类型",
  "message": "详细错误信息",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 使用建议

1. **连接检查**: 在开始使用API前，建议先调用 `/health` 端点检查服务状态
2. **分页处理**: 获取对话列表时，建议使用分页参数避免一次性加载过多数据
3. **错误重试**: 对于网络错误，建议实现适当的重试机制
4. **状态监控**: 定期调用 `/status` 端点监控系统健康状态