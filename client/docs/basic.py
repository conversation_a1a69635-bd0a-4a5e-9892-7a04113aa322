#!/usr/bin/env python3
"""
ChatGPT Controller API 基础调用示例

这是一个使用 Python requests 库调用 ChatGPT Controller API 的完整示例。
包含了所有主要功能的基本用法，可以作为单文件直接运行。

使用方法:
    python 基础调用示例.py

依赖:
    pip install requests
"""

import requests
import json
import time
from typing import Dict, Any, Optional, List
from datetime import datetime


class ChatGPTControllerClient:
    """ChatGPT Controller API 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化客户端
        
        Args:
            base_url: API服务器地址
        """
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/api/v1"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发送HTTP请求的通用方法
        
        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            endpoint: API端点
            **kwargs: requests库的其他参数
            
        Returns:
            API响应的JSON数据
            
        Raises:
            requests.RequestException: 网络请求错误
            ValueError: JSON解析错误
        """
        url = f"{self.api_base}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()  # 抛出HTTP错误
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求错误: {e}")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
            raise
    
    def get_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        print("📊 获取系统状态...")
        return self._make_request('GET', '/status')
    
    def get_health(self) -> Dict[str, Any]:
        """快速健康检查"""
        print("🏥 进行健康检查...")
        return self._make_request('GET', '/health')
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        print("📈 获取系统指标...")
        return self._make_request('GET', '/metrics')
    
    def list_conversations(self, page: int = 1, page_size: int = 20, 
                          status: Optional[str] = None, 
                          mode: Optional[str] = None) -> Dict[str, Any]:
        """
        获取对话列表
        
        Args:
            page: 页码
            page_size: 每页数量
            status: 状态筛选 (active, archived, deleted)
            mode: 模式筛选 (research, reason, search, canvas, picture_v2)
        """
        print(f"💬 获取对话列表 (第{page}页)...")
        
        params = {
            'page': page,
            'page_size': page_size
        }
        
        if status:
            params['status'] = status
        if mode:
            params['mode'] = mode
            
        return self._make_request('GET', '/conversations', params=params)
    
    def create_conversation(self, initial_prompt: str, 
                          mode: str = "research", 
                          title: Optional[str] = None) -> Dict[str, Any]:
        """
        创建新对话
        
        Args:
            initial_prompt: 初始提示词
            mode: 对话模式
            title: 对话标题
        """
        print(f"🆕 创建新对话: {title or '无标题'}...")
        
        data = {
            'initial_prompt': initial_prompt,
            'mode': mode
        }
        
        if title:
            data['title'] = title
            
        return self._make_request('POST', '/conversations', json=data)
    
    def get_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """获取对话详情"""
        print(f"🔍 获取对话详情: {conversation_id}...")
        return self._make_request('GET', f'/conversations/{conversation_id}')
    
    def send_message(self, conversation_id: str, content: str) -> Dict[str, Any]:
        """
        发送消息到对话
        
        Args:
            conversation_id: 对话ID
            content: 消息内容
        """
        print(f"📤 发送消息到对话: {conversation_id}...")
        
        data = {'content': content}
        return self._make_request('POST', f'/conversations/{conversation_id}/messages', json=data)


def print_separator(title: str):
    """打印分隔线"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)


def print_json(data: Dict[str, Any], title: str = "响应数据"):
    """格式化打印JSON数据"""
    print(f"\n📋 {title}:")
    print(json.dumps(data, ensure_ascii=False, indent=2))


def demo_system_status(client: ChatGPTControllerClient):
    """演示系统状态相关功能"""
    print_separator("系统状态检查")
    
    try:
        # 1. 健康检查
        health = client.get_health()
        print_json(health, "健康检查结果")
        
        # 2. 完整状态
        status = client.get_status()
        print_json(status, "系统状态")
        
        # 3. 系统指标
        metrics = client.get_metrics()
        print_json(metrics, "系统指标")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统状态检查失败: {e}")
        return False


def demo_conversation_management(client: ChatGPTControllerClient):
    """演示对话管理功能"""
    print_separator("对话管理演示")
    
    try:
        # 1. 获取现有对话列表
        conversations = client.list_conversations(page=1, page_size=5)
        print_json(conversations, "对话列表")
        
        # 2. 创建新对话
        new_conversation = client.create_conversation(
            initial_prompt="你好，我想了解一下人工智能的发展历史",
            mode="research",
            title="AI发展历史讨论"
        )
        print_json(new_conversation, "新对话创建结果")
        
        if new_conversation.get('success'):
            conversation_id = new_conversation['conversation']['id']
            
            # 3. 获取对话详情
            conversation_details = client.get_conversation(conversation_id)
            print_json(conversation_details, "对话详情")
            
            # 4. 发送消息
            message_response = client.send_message(
                conversation_id, 
                "请详细介绍一下深度学习的发展历程"
            )
            print_json(message_response, "消息发送结果")
            
            # 5. 再次获取对话详情查看新消息
            time.sleep(1)  # 等待一秒确保消息已处理
            updated_conversation = client.get_conversation(conversation_id)
            print_json(updated_conversation, "更新后的对话详情")
            
            return conversation_id
        
    except Exception as e:
        print(f"❌ 对话管理演示失败: {e}")
        return None


def demo_conversation_filtering(client: ChatGPTControllerClient):
    """演示对话筛选功能"""
    print_separator("对话筛选演示")
    
    try:
        # 1. 按状态筛选
        active_conversations = client.list_conversations(status="active", page_size=10)
        print_json(active_conversations, "活跃对话列表")
        
        # 2. 按模式筛选
        research_conversations = client.list_conversations(mode="research", page_size=10)
        print_json(research_conversations, "研究模式对话列表")
        
        # 3. 组合筛选
        filtered_conversations = client.list_conversations(
            status="active", 
            mode="research", 
            page_size=5
        )
        print_json(filtered_conversations, "组合筛选结果")
        
    except Exception as e:
        print(f"❌ 对话筛选演示失败: {e}")


def main():
    """主函数 - 完整的API调用演示"""
    print("🚀 ChatGPT Controller API 基础调用示例")
    print("=" * 60)
    print("本示例将演示如何使用 requests 库调用 ChatGPT Controller API")
    print("确保API服务器正在运行在 http://localhost:8000")
    
    # 初始化客户端
    client = ChatGPTControllerClient()
    
    # 1. 系统状态检查
    system_ok = demo_system_status(client)
    
    if not system_ok:
        print("\n❌ 系统状态检查失败，请确保API服务器正在运行")
        return
    
    # 2. 对话管理演示
    conversation_id = demo_conversation_management(client)
    
    # 3. 对话筛选演示
    demo_conversation_filtering(client)
    
    # 4. 总结
    print_separator("演示完成")
    print("✅ 所有API调用演示已完成！")
    
    if conversation_id:
        print(f"📝 创建的测试对话ID: {conversation_id}")
        print("💡 您可以使用这个ID继续测试其他功能")
    
    print("\n📚 更多信息:")
    print("- API文档: docs/API使用文档.md")
    print("- 高级用法: docs/高级用法示例.py")
    print("- 错误处理: docs/错误处理指南.md")


def interactive_demo():
    """交互式演示模式"""
    print("🎮 进入交互式演示模式")
    print("您可以手动测试各种API功能")

    client = ChatGPTControllerClient()

    while True:
        print("\n" + "="*40)
        print("请选择要测试的功能:")
        print("1. 系统状态检查")
        print("2. 获取对话列表")
        print("3. 创建新对话")
        print("4. 发送消息")
        print("5. 获取对话详情")
        print("0. 退出")

        choice = input("\n请输入选项 (0-5): ").strip()

        try:
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                status = client.get_status()
                print_json(status)
            elif choice == "2":
                conversations = client.list_conversations()
                print_json(conversations)
            elif choice == "3":
                prompt = input("请输入初始提示词: ").strip()
                title = input("请输入对话标题 (可选): ").strip() or None
                mode = input("请输入对话模式 (research/reason/search/canvas/picture_v2, 默认research): ").strip() or "research"

                result = client.create_conversation(prompt, mode, title)
                print_json(result)
            elif choice == "4":
                conv_id = input("请输入对话ID: ").strip()
                message = input("请输入消息内容: ").strip()

                result = client.send_message(conv_id, message)
                print_json(result)
            elif choice == "5":
                conv_id = input("请输入对话ID: ").strip()

                result = client.get_conversation(conv_id)
                print_json(result)
            else:
                print("❌ 无效选项，请重新选择")

        except Exception as e:
            print(f"❌ 操作失败: {e}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_demo()
    else:
        main()
