#!/usr/bin/env python3
"""
ChatGPT Controller API 高级用法示例

演示更复杂的使用场景，包括：
- 错误处理和重试机制
- 批量操作
- 异步处理
- 配置管理
- 日志记录

使用方法:
    python 高级用法示例.py

依赖:
    pip install requests aiohttp asyncio
"""

import requests
import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from functools import wraps
import random


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_client.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class APIError(Exception):
    """API错误基类"""
    pass


class ConnectionError(APIError):
    """连接错误"""
    pass


class ValidationError(APIError):
    """验证错误"""
    pass


class ServerError(APIError):
    """服务器错误"""
    pass


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """重试装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except (requests.exceptions.RequestException, ConnectionError) as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = delay * (backoff ** attempt)
                        logger.warning(f"第{attempt + 1}次尝试失败，{wait_time:.1f}秒后重试: {e}")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"所有重试都失败了: {e}")
                        
            raise last_exception
        return wrapper
    return decorator


class AdvancedChatGPTClient:
    """高级ChatGPT Controller API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000", timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/api/v1"
        self.timeout = timeout
        
        # 配置session
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'ChatGPT-Controller-Client/1.0'
        })
        
        # 统计信息
        self.stats = {
            'requests_made': 0,
            'requests_failed': 0,
            'total_response_time': 0.0
        }
    
    @retry_on_failure(max_retries=3, delay=1.0, backoff=2.0)
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求（带重试机制）"""
        url = f"{self.api_base}/{endpoint.lstrip('/')}"
        start_time = time.time()
        
        try:
            self.stats['requests_made'] += 1
            logger.info(f"发送请求: {method} {url}")
            
            response = self.session.request(
                method, url, timeout=self.timeout, **kwargs
            )
            
            response_time = time.time() - start_time
            self.stats['total_response_time'] += response_time
            
            logger.info(f"请求完成: {response.status_code} ({response_time:.2f}s)")
            
            # 处理不同的HTTP状态码
            if response.status_code == 400:
                raise ValidationError(f"请求参数错误: {response.text}")
            elif response.status_code == 404:
                raise APIError(f"资源不存在: {response.text}")
            elif response.status_code >= 500:
                raise ServerError(f"服务器错误: {response.text}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.Timeout:
            self.stats['requests_failed'] += 1
            raise ConnectionError(f"请求超时 ({self.timeout}s)")
        except requests.exceptions.ConnectionError:
            self.stats['requests_failed'] += 1
            raise ConnectionError("无法连接到服务器")
        except requests.exceptions.RequestException as e:
            self.stats['requests_failed'] += 1
            raise APIError(f"请求失败: {e}")
        except json.JSONDecodeError:
            self.stats['requests_failed'] += 1
            raise APIError("响应不是有效的JSON格式")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取客户端统计信息"""
        avg_response_time = (
            self.stats['total_response_time'] / self.stats['requests_made']
            if self.stats['requests_made'] > 0 else 0
        )
        
        return {
            **self.stats,
            'average_response_time': avg_response_time,
            'success_rate': (
                (self.stats['requests_made'] - self.stats['requests_failed']) 
                / self.stats['requests_made'] * 100
                if self.stats['requests_made'] > 0 else 0
            )
        }
    
    def batch_get_conversations(self, conversation_ids: List[str]) -> List[Dict[str, Any]]:
        """批量获取对话详情"""
        logger.info(f"批量获取{len(conversation_ids)}个对话的详情")
        results = []
        
        for i, conv_id in enumerate(conversation_ids):
            try:
                logger.info(f"获取对话 {i+1}/{len(conversation_ids)}: {conv_id}")
                conversation = self._make_request('GET', f'/conversations/{conv_id}')
                results.append(conversation)
                
                # 避免请求过于频繁
                if i < len(conversation_ids) - 1:
                    time.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"获取对话 {conv_id} 失败: {e}")
                results.append({'error': str(e), 'conversation_id': conv_id})
        
        return results
    
    def create_conversation_with_messages(self, initial_prompt: str, 
                                        follow_up_messages: List[str],
                                        mode: str = "research",
                                        title: Optional[str] = None) -> Dict[str, Any]:
        """创建对话并发送多条消息"""
        logger.info(f"创建对话并发送{len(follow_up_messages)}条后续消息")
        
        # 创建对话
        conversation = self._make_request('POST', '/conversations', json={
            'initial_prompt': initial_prompt,
            'mode': mode,
            'title': title
        })
        
        if not conversation.get('success'):
            raise APIError(f"创建对话失败: {conversation}")
        
        conversation_id = conversation['conversation']['id']
        logger.info(f"对话创建成功: {conversation_id}")
        
        # 发送后续消息
        message_results = []
        for i, message in enumerate(follow_up_messages):
            try:
                logger.info(f"发送消息 {i+1}/{len(follow_up_messages)}")
                result = self._make_request(
                    'POST', 
                    f'/conversations/{conversation_id}/messages',
                    json={'content': message}
                )
                message_results.append(result)
                
                # 等待一段时间确保消息处理完成
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"发送消息失败: {e}")
                message_results.append({'error': str(e)})
        
        return {
            'conversation': conversation,
            'message_results': message_results
        }
    
    def monitor_system_health(self, duration: int = 60, interval: int = 10):
        """监控系统健康状态"""
        logger.info(f"开始监控系统健康状态 ({duration}秒，每{interval}秒检查一次)")
        
        start_time = time.time()
        health_history = []
        
        while time.time() - start_time < duration:
            try:
                health = self._make_request('GET', '/health')
                status = self._make_request('GET', '/status')
                
                health_record = {
                    'timestamp': datetime.now().isoformat(),
                    'health': health,
                    'status': status
                }
                health_history.append(health_record)
                
                # 检查是否有问题
                if not status.get('success'):
                    logger.warning("系统状态异常!")
                
                if not status.get('system_health', {}).get('database_healthy'):
                    logger.error("数据库健康检查失败!")
                
                if not status.get('system_health', {}).get('websocket_healthy'):
                    logger.error("WebSocket健康检查失败!")
                
                logger.info(f"健康检查完成 - 状态: {health.get('status', 'unknown')}")
                
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
                health_history.append({
                    'timestamp': datetime.now().isoformat(),
                    'error': str(e)
                })
            
            time.sleep(interval)
        
        logger.info(f"监控完成，共收集{len(health_history)}条记录")
        return health_history


class AsyncChatGPTClient:
    """异步ChatGPT Controller API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/api/v1"
    
    async def _make_request(self, session: aiohttp.ClientSession, 
                          method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """异步HTTP请求"""
        url = f"{self.api_base}/{endpoint.lstrip('/')}"
        
        async with session.request(method, url, **kwargs) as response:
            response.raise_for_status()
            return await response.json()
    
    async def batch_create_conversations(self, conversation_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """异步批量创建对话"""
        logger.info(f"异步批量创建{len(conversation_data)}个对话")
        
        async with aiohttp.ClientSession(
            headers={'Content-Type': 'application/json'}
        ) as session:
            tasks = []
            
            for data in conversation_data:
                task = self._make_request(
                    session, 'POST', '/conversations', json=data
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"创建对话 {i+1} 失败: {result}")
                    processed_results.append({'error': str(result)})
                else:
                    processed_results.append(result)
            
            return processed_results


def demo_error_handling():
    """演示错误处理"""
    print("\n🛡️ 错误处理演示")
    print("="*50)
    
    client = AdvancedChatGPTClient()
    
    # 测试连接错误
    try:
        wrong_client = AdvancedChatGPTClient("http://localhost:9999")
        wrong_client._make_request('GET', '/status')
    except ConnectionError as e:
        print(f"✅ 成功捕获连接错误: {e}")
    
    # 测试验证错误
    try:
        client._make_request('POST', '/conversations', json={})
    except ValidationError as e:
        print(f"✅ 成功捕获验证错误: {e}")
    except Exception as e:
        print(f"⚠️ 其他错误: {e}")


def demo_batch_operations():
    """演示批量操作"""
    print("\n📦 批量操作演示")
    print("="*50)
    
    client = AdvancedChatGPTClient()
    
    try:
        # 获取对话列表
        conversations = client._make_request('GET', '/conversations', params={'page_size': 5})
        
        if conversations.get('conversations'):
            conv_ids = [conv['id'] for conv in conversations['conversations'][:3]]
            
            # 批量获取对话详情
            batch_results = client.batch_get_conversations(conv_ids)
            print(f"✅ 批量获取了{len(batch_results)}个对话的详情")
            
            for i, result in enumerate(batch_results):
                if 'error' in result:
                    print(f"  对话 {i+1}: 错误 - {result['error']}")
                else:
                    print(f"  对话 {i+1}: 成功 - {result.get('conversation', {}).get('title', '无标题')}")
        
        # 创建对话并发送多条消息
        result = client.create_conversation_with_messages(
            initial_prompt="你好，我想学习Python编程",
            follow_up_messages=[
                "请推荐一些Python学习资源",
                "如何开始第一个Python项目？"
            ],
            title="Python学习讨论"
        )
        
        print(f"✅ 创建对话并发送消息完成")
        print(f"  对话ID: {result['conversation']['conversation']['id']}")
        print(f"  消息发送结果: {len(result['message_results'])}条")
        
    except Exception as e:
        print(f"❌ 批量操作失败: {e}")


async def demo_async_operations():
    """演示异步操作"""
    print("\n⚡ 异步操作演示")
    print("="*50)
    
    client = AsyncChatGPTClient()
    
    # 准备测试数据
    conversation_data = [
        {
            'initial_prompt': f'这是测试对话 {i+1}',
            'mode': 'research',
            'title': f'异步测试对话 {i+1}'
        }
        for i in range(3)
    ]
    
    try:
        start_time = time.time()
        results = await client.batch_create_conversations(conversation_data)
        end_time = time.time()
        
        print(f"✅ 异步创建{len(results)}个对话完成")
        print(f"  耗时: {end_time - start_time:.2f}秒")
        
        success_count = sum(1 for r in results if 'error' not in r)
        print(f"  成功: {success_count}/{len(results)}")
        
    except Exception as e:
        print(f"❌ 异步操作失败: {e}")


def demo_monitoring():
    """演示系统监控"""
    print("\n📊 系统监控演示")
    print("="*50)
    
    client = AdvancedChatGPTClient()
    
    try:
        # 短时间监控演示
        health_history = client.monitor_system_health(duration=20, interval=5)
        
        print(f"✅ 监控完成，收集了{len(health_history)}条记录")
        
        # 分析监控结果
        healthy_count = sum(
            1 for record in health_history 
            if 'error' not in record and record.get('health', {}).get('status') == 'healthy'
        )
        
        print(f"  健康检查通过率: {healthy_count}/{len(health_history)} ({healthy_count/len(health_history)*100:.1f}%)")
        
    except Exception as e:
        print(f"❌ 监控失败: {e}")


def main():
    """主函数"""
    print("🚀 ChatGPT Controller API 高级用法演示")
    print("="*60)
    
    # 1. 错误处理演示
    demo_error_handling()
    
    # 2. 批量操作演示
    demo_batch_operations()
    
    # 3. 异步操作演示
    print("\n⚡ 开始异步操作演示...")
    asyncio.run(demo_async_operations())
    
    # 4. 系统监控演示
    demo_monitoring()
    
    # 5. 显示客户端统计
    client = AdvancedChatGPTClient()
    stats = client.get_stats()
    print(f"\n📈 客户端统计信息:")
    print(f"  总请求数: {stats['requests_made']}")
    print(f"  失败请求数: {stats['requests_failed']}")
    print(f"  成功率: {stats['success_rate']:.1f}%")
    print(f"  平均响应时间: {stats['average_response_time']:.2f}秒")
    
    print("\n✅ 高级用法演示完成！")


if __name__ == "__main__":
    main()
