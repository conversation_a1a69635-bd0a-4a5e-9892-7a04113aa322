# ChatGPT Controller API 文档

欢迎使用 ChatGPT Controller API 文档！本目录包含了完整的API使用指南、示例代码和最佳实践。

## 📚 文档目录

### 1. [API使用文档](./API使用文档.md)
完整的API接口文档，包括：
- 所有端点的详细说明
- 请求/响应格式
- 参数说明和示例
- 对话模式和状态说明
- 使用建议和最佳实践

### 2. [基础调用示例](./基础调用示例.py)
使用 Python requests 库的完整示例，包括：
- 单文件实现，依赖最少
- 所有主要功能的基本用法
- 系统状态检查
- 对话管理（创建、获取、发送消息）
- 交互式演示模式
- 错误处理示例

**运行方式**：
```bash
# 基础演示
python 基础调用示例.py

# 交互式模式
python 基础调用示例.py --interactive
```

## 🚀 快速开始

### 1. 安装依赖

对于基础示例：
```bash
pip install requests
```

对于高级示例：
```bash
pip install requests aiohttp
```

### 2. 确保API服务器运行

确保 ChatGPT Controller API 服务器正在运行：
```bash
# 检查服务器状态
curl http://localhost:8000/api/v1/health
```

### 3. 运行示例

```bash
# 进入文档目录
cd docs

# 运行基础示例
python 基础调用示例.py

# 运行高级示例
python 高级用法示例.py
```

## 📋 API 端点概览

| 分类 | 端点 | 方法 | 描述 |
|------|------|------|------|
| **系统状态** | `/health` | GET | 快速健康检查 |
| | `/status` | GET | 完整系统状态 |
| | `/metrics` | GET | 系统指标 |
| **对话管理** | `/conversations` | GET | 获取对话列表 |
| | `/conversations` | POST | 创建新对话 |
| | `/conversations/{id}` | GET | 获取对话详情 |
| | `/conversations/{id}/messages` | POST | 发送消息 |

## 🛠️ 使用场景

### 基础用法
- 系统健康检查
- 创建和管理对话
- 发送消息
- 获取对话历史

### 高级用法
- 批量处理对话
- 异步操作
- 错误重试
- 系统监控
- 性能优化

## 📖 代码示例

### 快速健康检查
```python
import requests

response = requests.get("http://localhost:8000/api/v1/health")
print(response.json())
```

### 创建对话
```python
import requests

data = {
    "initial_prompt": "你好，我想了解人工智能",
    "mode": "research",
    "title": "AI学习讨论"
}

response = requests.post(
    "http://localhost:8000/api/v1/conversations",
    json=data
)
print(response.json())
```

### 发送消息
```python
import requests

conversation_id = "conv_123456789"
data = {"content": "请详细介绍深度学习"}

response = requests.post(
    f"http://localhost:8000/api/v1/conversations/{conversation_id}/messages",
    json=data
)
print(response.json())
```

## 🔧 配置说明

### 基础配置
- **基础URL**: `http://localhost:8000`
- **API版本**: `v1`
- **超时时间**: 30秒
- **数据格式**: JSON

### 对话模式
- `research`: 研究模式，适合深入讨论
- `reason`: 推理模式，适合逻辑分析
- `search`: 搜索模式，适合信息查询
- `canvas`: 画布模式，适合创意工作
- `picture_v2`: 图片模式，适合图像任务

### 对话状态
- `active`: 活跃对话
- `archived`: 已归档对话
- `deleted`: 已删除对话
